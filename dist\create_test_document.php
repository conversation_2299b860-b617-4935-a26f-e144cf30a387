<?php
// =================================================================
// Script: Crear Documento de Prueba para InteletGroup
// Descripción: Crea un documento de prueba para verificar descarga
// =================================================================

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar autenticación (simulada para pruebas)
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1; // Cambiar por un ID válido
    $_SESSION['proyecto'] = 'inteletGroup';
    $_SESSION['nombre_usuario'] = 'Usuario de Prueba';
}

require_once 'con_db.php';

echo "<h1>📄 Crear Documento de Prueba</h1>";
echo "<hr>";

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    die("❌ Error de conexión a la base de datos");
}

$usuario_id = $_SESSION['usuario_id'];

// Verificar si existe un prospecto para este usuario
$stmt = $mysqli->prepare("SELECT id, rut_cliente, razon_social FROM tb_inteletgroup_prospectos WHERE usuario_id = ? LIMIT 1");
$stmt->bind_param("i", $usuario_id);
$stmt->execute();

$prospecto_id = $rut_cliente = $razon_social = null;
$stmt->bind_result($prospecto_id, $rut_cliente, $razon_social);

if (!$stmt->fetch()) {
    $stmt->close();
    echo "<p>❌ No se encontró ningún prospecto para el usuario ID: $usuario_id</p>";
    echo "<p>Primero debes crear un prospecto en <a href='form_inteletgroup.php'>form_inteletgroup.php</a></p>";
    exit;
}
$stmt->close();

echo "<p>✅ Prospecto encontrado: <strong>$razon_social</strong> (RUT: $rut_cliente)</p>";

// Crear directorio de uploads si no existe
$upload_dir = 'uploads/inteletgroup_prospectos/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "<p>✅ Directorio de uploads creado: $upload_dir</p>";
    } else {
        die("❌ Error al crear directorio de uploads");
    }
} else {
    echo "<p>✅ Directorio de uploads existe: $upload_dir</p>";
}

// Crear archivo de prueba
$test_content = "Este es un documento de prueba para InteletGroup\n";
$test_content .= "RUT Cliente: $rut_cliente\n";
$test_content .= "Razón Social: $razon_social\n";
$test_content .= "Fecha de creación: " . date('Y-m-d H:i:s') . "\n";
$test_content .= "Usuario ID: $usuario_id\n";
$test_content .= "\nEste archivo fue creado automáticamente para probar la funcionalidad de descarga de documentos.";

$file_name = "documento_prueba.txt";
$unique_name = $rut_cliente . '_' . time() . '_prueba.txt';
$file_path = $upload_dir . $unique_name;

if (file_put_contents($file_path, $test_content)) {
    echo "<p>✅ Archivo de prueba creado: $file_path</p>";
    
    // Insertar en la base de datos
    $stmt = $mysqli->prepare("
        INSERT INTO tb_inteletgroup_documentos (
            prospecto_id, usuario_id, rut_cliente, nombre_archivo, 
            nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $file_size = filesize($file_path);
    $tipo_archivo = 'text/plain';
    
    $stmt->bind_param("iissssis", 
        $prospecto_id, $usuario_id, $rut_cliente, $unique_name,
        $file_name, $tipo_archivo, $file_size, $file_path
    );
    
    if ($stmt->execute()) {
        $documento_id = $mysqli->insert_id;
        echo "<p>✅ Documento registrado en base de datos con ID: $documento_id</p>";
        
        echo "<hr>";
        echo "<h2>🧪 Probar Descarga</h2>";
        echo "<p><a href='descargar_documento.php?id=$documento_id&action=view' target='_blank'>👁️ Ver Documento</a></p>";
        echo "<p><a href='descargar_documento.php?id=$documento_id&action=download'>💾 Descargar Documento</a></p>";
        
        echo "<hr>";
        echo "<h2>📋 Información del Documento</h2>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> $documento_id</li>";
        echo "<li><strong>Nombre Original:</strong> $file_name</li>";
        echo "<li><strong>Nombre en Servidor:</strong> $unique_name</li>";
        echo "<li><strong>Ruta:</strong> $file_path</li>";
        echo "<li><strong>Tamaño:</strong> $file_size bytes</li>";
        echo "<li><strong>Tipo:</strong> $tipo_archivo</li>";
        echo "</ul>";
        
    } else {
        echo "<p>❌ Error al registrar documento en base de datos: " . $stmt->error . "</p>";
    }
    
} else {
    echo "<p>❌ Error al crear archivo de prueba</p>";
}

echo "<br><hr>";
echo "<p><a href='test_document_download.php'>🧪 Ver Prueba de Descarga</a></p>";
echo "<p><a href='inteletgroup_documentos.php'>🔙 Volver a Gestión de Documentos</a></p>";
?>
