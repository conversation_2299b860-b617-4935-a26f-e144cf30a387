<?php
/**
 * Endpoint para obtener tipos de documentos según el tipo de persona
 * Devuelve los documentos desde la base de datos incluyendo los complementarios
 */

// Activar reporte de errores para debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Log inmediato para verificar que el script se ejecuta
error_log("===== INICIO obtener_tipos_documento.php =====");
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
error_log("PHP Version: " . phpversion());

// Establecer headers antes de cualquier salida
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    error_log("Preflight request recibido, respondiendo con OK");
    exit(0);
}

// Solo permitir POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

try {
    error_log("Iniciando proceso principal del endpoint...");
    // Verificar que el archivo de conexión existe
    $db_file = dirname(__DIR__) . '/con_db.php';
    error_log("Ruta del archivo de conexión: " . $db_file);
    error_log("¿Archivo existe?: " . (file_exists($db_file) ? 'Sí' : 'No'));
    
    if (!file_exists($db_file)) {
        throw new Exception('Archivo de conexión no encontrado en: ' . $db_file);
    }
    
    // Incluir conexión a la base de datos
    require_once $db_file;

    // Log para debug
    error_log("Conexión incluida exitosamente");
    error_log("Variables disponibles después de incluir con_db.php:");
    error_log("- conn: " . (isset($conn) ? 'Sí' : 'No'));
    error_log("- mysqli: " . (isset($mysqli) ? 'Sí' : 'No'));
    
    // Verificar que tenemos conexión
    if (!isset($conn) && !isset($mysqli)) {
        throw new Exception('No se pudo establecer conexión con la base de datos');
    }
    
    // Usar la variable de conexión disponible
    $db = isset($conn) ? $conn : (isset($mysqli) ? $mysqli : null);
    if (!$db) {
        throw new Exception('Conexión a base de datos no disponible');
    }

    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    error_log("obtener_tipos_documento.php: Input recibido: " . json_encode($input));
    
    if (!isset($input['tipo_persona']) || empty($input['tipo_persona'])) {
        error_log("obtener_tipos_documento.php: Error - Tipo de persona no proporcionado");
        throw new Exception('Tipo de persona es requerido');
    }

    $tipo_persona = $input['tipo_persona'];
    error_log("obtener_tipos_documento.php: Tipo de persona: " . $tipo_persona);

    // Validar tipo de persona
    if (!in_array($tipo_persona, ['Natural', 'Juridica'])) {
        error_log("obtener_tipos_documento.php: Error - Tipo de persona inválido: " . $tipo_persona);
        throw new Exception('Tipo de persona inválido');
    }
    
    // Consultar tipos de documentos
    $sql = "SELECT
                id,
                codigo,
                nombre,
                descripcion,
                es_obligatorio,
                orden,
                tipo_persona
            FROM tb_inteletgroup_tipos_documento
            WHERE (tipo_persona = ? OR tipo_persona = 'Ambos')
            AND estado = 'Activo'
            ORDER BY
                es_obligatorio DESC,  -- Obligatorios primero
                orden ASC";

    error_log("Preparando consulta SQL...");
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        error_log("Error al preparar consulta: " . $db->error);
        throw new Exception('Error preparando consulta: ' . $db->error);
    }
    error_log("Consulta preparada exitosamente")
    
    $stmt->bind_param("s", $tipo_persona);
    
    if (!$stmt->execute()) {
        throw new Exception('Error ejecutando consulta: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $documentos = [];
    
    while ($row = $result->fetch_assoc()) {
        $documentos[] = [
            'id' => (int)$row['id'],
            'codigo' => $row['codigo'],
            'nombre' => $row['nombre'],
            'descripcion' => $row['descripcion'],
            'es_obligatorio' => (int)$row['es_obligatorio'],
            'orden' => (int)$row['orden'],
            'tipo_persona' => $row['tipo_persona']
        ];
    }
    
    $stmt->close();
    
    // Log para debug
    error_log("Documentos encontrados para $tipo_persona: " . count($documentos));
    error_log("Documentos: " . json_encode($documentos));
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'documentos' => $documentos,
        'total' => count($documentos),
        'obligatorios' => count(array_filter($documentos, function($doc) {
            return $doc['es_obligatorio'] === 1;
        })),
        'opcionales' => count(array_filter($documentos, function($doc) {
            return $doc['es_obligatorio'] === 0;
        }))
    ]);
    
} catch (Exception $e) {
    error_log("ERROR CAPTURADO en obtener_tipos_documento.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
} catch (Error $e) {
    // Capturar errores fatales
    error_log("ERROR FATAL en obtener_tipos_documento.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error fatal: ' . $e->getMessage(),
        'debug' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
} finally {
    if (isset($db) && $db) {
        $db->close();
    }
    error_log("===== FIN obtener_tipos_documento.php =====");
}
?>
