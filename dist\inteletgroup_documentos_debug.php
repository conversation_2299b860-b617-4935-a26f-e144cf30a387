<?php
// Versión de debug paso a paso
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PASO 1: Inicio del script<br>";

// Paso 2: Incluir cache_utils
echo "PASO 2: Incluyendo cache_utils.php<br>";
require_once 'cache_utils.php';
echo "✓ cache_utils.php cargado<br>";

// Paso 3: Iniciar sesión
echo "PASO 3: Iniciando se<PERSON><br>";
session_start();
echo "✓ Sesión iniciada<br>";

// Paso 4: Verificar autenticación
echo "PASO 4: Verificando autenticación<br>";
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    echo "✗ No autenticado<br>";
    exit;
}
echo "✓ Usuario autenticado: " . $_SESSION['usuario_id'] . "<br>";

// Paso 5: Incluir con_db
echo "PASO 5: Incluyendo con_db.php<br>";
require_once 'con_db.php';
echo "✓ con_db.php cargado<br>";

// Paso 6: Verificar mysqli
echo "PASO 6: Verificando conexión mysqli<br>";
if (!isset($mysqli)) {
    echo "✗ mysqli no definida<br>";
    exit;
}
echo "✓ mysqli definida<br>";

$conexion = $mysqli;

// Paso 7: Verificar prospecto_id
echo "PASO 7: Verificando prospecto_id<br>";
$prospecto_id = isset($_GET['prospecto_id']) ? intval($_GET['prospecto_id']) : null;
echo "Prospecto ID: " . ($prospecto_id ?? 'No especificado') . "<br>";

if ($prospecto_id) {
    // Paso 8: Buscar prospecto
    echo "PASO 8: Buscando prospecto en BD<br>";
    $stmt = $conexion->prepare("SELECT id, razon_social FROM tb_inteletgroup_prospectos WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $prospecto_id);
        $stmt->execute();
        $stmt->bind_result($id, $razon_social);
        if ($stmt->fetch()) {
            echo "✓ Prospecto encontrado: $razon_social<br>";
        } else {
            echo "✗ Prospecto no encontrado<br>";
        }
        $stmt->close();
    } else {
        echo "✗ Error preparando consulta<br>";
    }
}

// Paso 9: Probar función no_cache_meta
echo "PASO 9: Probando función no_cache_meta()<br>";
$meta_tags = no_cache_meta();
echo "✓ Función ejecutada, longitud del resultado: " . strlen($meta_tags) . "<br>";

// Paso 10: Generar HTML básico
echo "PASO 10: Generando HTML<br>";
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug</title>
    <?php echo no_cache_meta(); ?>
</head>
<body>
    <h1>Debug Completado</h1>
    <p>Si ves este mensaje, el problema está en otra parte del código enhanced.</p>
    <a href="inteletgroup_documentos_enhanced.php<?php echo $prospecto_id ? '?prospecto_id=' . $prospecto_id : ''; ?>">
        Probar versión enhanced
    </a>
</body>
</html>