-- =====================================================
-- SCRIPT PARA AGREGAR DOCUMENTOS COMPLEMENTARIOS
-- Documentos que aplican tanto para Persona Natural como Jurídica
-- =====================================================

-- OPCIÓN 1: Crear documentos con tipo_persona = 'Ambos' (RECOMENDADO)
-- Esta opción es más eficiente y evita duplicación

-- 1. Mandato PAC (convertir a tipo 'Ambos' y agregar para Jurídica)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES ('AMBOS_MANDATO_PAC', 'Mandato PAC', 'Clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento', 'Ambos', 0, 20, 'Activo');

-- 2. Cuentas de abono de terceros (nuevo para ambos tipos)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES ('AMBOS_CUENTAS_TERCEROS', 'Cuentas de abono de terceros', 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)', 'Ambos', 0, 21, 'Activo');

-- 3. Poder Notarial para cuentas de terceros (convertir a tipo 'Ambos')
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES ('AMBOS_PODER_NOTARIAL', 'Poder Notarial para cuentas de terceros', 'Clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento', 'Ambos', 0, 22, 'Activo');

-- 4. Sociedad de Hecho - Declaración Jurada Notarial (nuevo para ambos tipos)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES ('AMBOS_SOCIEDAD_HECHO', 'Sociedad de Hecho - Declaración Jurada Notarial', 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)', 'Ambos', 0, 23, 'Activo');

-- =====================================================
-- OPCIÓN 2: Crear documentos separados por tipo de persona (ALTERNATIVA)
-- Descomenta estas líneas si prefieres mantener documentos separados
-- =====================================================

/*
-- 1. Mandato PAC para Jurídica (ya existe para Natural)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES ('PJ_MANDATO_PAC', 'Mandato PAC', 'Clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento', 'Juridica', 0, 9, 'Activo');

-- 2. Cuentas de abono de terceros (para ambos tipos por separado)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES
('PN_CUENTAS_TERCEROS', 'Cuentas de abono de terceros', 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)', 'Natural', 0, 11, 'Activo'),
('PJ_CUENTAS_TERCEROS', 'Cuentas de abono de terceros', 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)', 'Juridica', 0, 10, 'Activo');

-- 3. Poder Notarial para Jurídica (ya existe para Natural)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES ('PJ_PODER_NOTARIAL', 'Poder Notarial para cuentas de terceros', 'Clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento', 'Juridica', 0, 11, 'Activo');

-- 4. Sociedad de Hecho (para ambos tipos por separado)
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden, estado)
VALUES
('PN_SOCIEDAD_HECHO', 'Sociedad de Hecho - Declaración Jurada Notarial', 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)', 'Natural', 0, 12, 'Activo'),
('PJ_SOCIEDAD_HECHO', 'Sociedad de Hecho - Declaración Jurada Notarial', 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)', 'Juridica', 0, 12, 'Activo');
*/

-- =====================================================
-- VERIFICACIÓN: Consultar todos los documentos después de la inserción
-- =====================================================

-- Ver todos los documentos organizados por tipo de persona
SELECT
    id,
    codigo,
    nombre,
    tipo_persona,
    es_obligatorio,
    orden,
    CASE
        WHEN es_obligatorio = 1 THEN 'Obligatorio'
        ELSE 'Opcional'
    END as tipo_documento
FROM tb_inteletgroup_tipos_documento
ORDER BY
    CASE tipo_persona
        WHEN 'Natural' THEN 1
        WHEN 'Juridica' THEN 2
        WHEN 'Ambos' THEN 3
    END,
    orden;

-- Ver solo los nuevos documentos complementarios
SELECT
    id,
    codigo,
    nombre,
    tipo_persona,
    orden
FROM tb_inteletgroup_tipos_documento
WHERE codigo LIKE 'AMBOS_%'
ORDER BY orden;

-- Contar documentos por tipo de persona
SELECT
    tipo_persona,
    COUNT(*) as total_documentos,
    SUM(CASE WHEN es_obligatorio = 1 THEN 1 ELSE 0 END) as obligatorios,
    SUM(CASE WHEN es_obligatorio = 0 THEN 1 ELSE 0 END) as opcionales
FROM tb_inteletgroup_tipos_documento
WHERE estado = 'Activo'
GROUP BY tipo_persona
ORDER BY tipo_persona;
