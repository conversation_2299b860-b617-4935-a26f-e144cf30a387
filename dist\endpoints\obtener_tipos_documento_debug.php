<?php
/**
 * Versión de debug del endpoint
 */

// Primero, escribir algo inmediatamente para verificar que el script se ejecuta
file_put_contents(__DIR__ . '/debug.log', date('Y-m-d H:i:s') . " - <PERSON><PERSON>t iniciado\n", FILE_APPEND);

// Activar todos los errores
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Log en archivo propio
function debug_log($msg) {
    file_put_contents(__DIR__ . '/debug.log', date('Y-m-d H:i:s') . " - " . $msg . "\n", FILE_APPEND);
}

debug_log("Headers enviados");

// Responder a OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    debug_log("OPTIONS request - saliendo");
    exit(0);
}

debug_log("Método: " . $_SERVER['REQUEST_METHOD']);

// Solo POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debug_log("Método no permitido");
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

debug_log("POST recibido, procesando...");

try {
    // Obtener input
    $input_raw = file_get_contents('php://input');
    debug_log("Input raw: " . $input_raw);
    
    $input = json_decode($input_raw, true);
    debug_log("Input decodificado: " . json_encode($input));
    
    if (!isset($input['tipo_persona'])) {
        throw new Exception('tipo_persona es requerido');
    }
    
    $tipo_persona = $input['tipo_persona'];
    debug_log("Tipo persona: " . $tipo_persona);
    
    // Verificar archivo de conexión
    $db_file = dirname(__DIR__) . '/con_db.php';
    debug_log("Buscando archivo DB en: " . $db_file);
    debug_log("¿Existe?: " . (file_exists($db_file) ? 'Sí' : 'No'));
    
    if (!file_exists($db_file)) {
        throw new Exception('Archivo de conexión no encontrado');
    }
    
    // Incluir conexión
    debug_log("Incluyendo conexión...");
    require_once $db_file;
    debug_log("Conexión incluida");
    
    // Verificar variables
    debug_log("Variables después de include:");
    debug_log("- conn: " . (isset($conn) ? 'Sí' : 'No'));
    debug_log("- mysqli: " . (isset($mysqli) ? 'Sí' : 'No'));
    
    $db = isset($conn) ? $conn : (isset($mysqli) ? $mysqli : null);
    if (!$db) {
        throw new Exception('No hay conexión disponible');
    }
    
    debug_log("Conexión obtenida, ejecutando consulta...");
    
    // Consulta simple primero
    $sql = "SELECT COUNT(*) as total FROM tb_inteletgroup_tipos_documento WHERE estado = 'Activo'";
    $result = $db->query($sql);
    
    if (!$result) {
        throw new Exception('Error en consulta: ' . $db->error);
    }
    
    $row = $result->fetch_assoc();
    debug_log("Total documentos activos: " . $row['total']);
    
    // Ahora la consulta real
    $sql = "SELECT id, codigo, nombre, descripcion, es_obligatorio, orden, tipo_persona
            FROM tb_inteletgroup_tipos_documento
            WHERE (tipo_persona = ? OR tipo_persona = 'Ambos')
            AND estado = 'Activo'
            ORDER BY es_obligatorio DESC, orden ASC";
    
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        throw new Exception('Error preparando consulta: ' . $db->error);
    }
    
    $stmt->bind_param("s", $tipo_persona);
    $stmt->execute();
    
    $result = $stmt->get_result();
    $documentos = [];
    
    while ($row = $result->fetch_assoc()) {
        $documentos[] = [
            'id' => (int)$row['id'],
            'codigo' => $row['codigo'],
            'nombre' => $row['nombre'],
            'descripcion' => $row['descripcion'],
            'es_obligatorio' => (int)$row['es_obligatorio'],
            'orden' => (int)$row['orden'],
            'tipo_persona' => $row['tipo_persona']
        ];
    }
    
    $stmt->close();
    
    debug_log("Documentos encontrados: " . count($documentos));
    
    // Respuesta
    $response = [
        'success' => true,
        'documentos' => $documentos,
        'total' => count($documentos),
        'debug_info' => [
            'tipo_persona' => $tipo_persona,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    debug_log("Enviando respuesta exitosa");
    echo json_encode($response);
    
} catch (Exception $e) {
    debug_log("ERROR: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug_file' => __DIR__ . '/debug.log'
    ]);
} finally {
    if (isset($db)) {
        $db->close();
    }
    debug_log("Script finalizado");
}
?>