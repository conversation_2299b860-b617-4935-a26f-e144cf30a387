<?php
// =================================================================
// Script de Prueba: Verificar Descarga de Documentos InteletGroup
// Descripción: Prueba la funcionalidad de descarga y visualización
// =================================================================

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar autenticación (simulada para pruebas)
if (!isset($_SESSION['usuario_id'])) {
    // Para pruebas, simular usuario autenticado
    $_SESSION['usuario_id'] = 1; // Cambiar por un ID válido
    $_SESSION['proyecto'] = 'inteletGroup';
    $_SESSION['nombre_usuario'] = 'Usuario de Prueba';
}

require_once 'con_db.php';

echo "<h1>🧪 Prueba de Descarga de Documentos InteletGroup</h1>";
echo "<hr>";

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    die("❌ Error de conexión a la base de datos");
}

echo "✅ Conexión a base de datos establecida<br><br>";

// Obtener documentos de prueba
$usuario_id = $_SESSION['usuario_id'];
echo "<h2>📋 Documentos disponibles para el usuario ID: $usuario_id</h2>";

$sql = "SELECT d.id, d.prospecto_id, d.usuario_id, d.rut_cliente, d.nombre_archivo, 
               d.nombre_original, d.tipo_archivo, d.tamaño_archivo, d.ruta_archivo, 
               d.fecha_subida, d.estado, p.razon_social
        FROM tb_inteletgroup_documentos d 
        LEFT JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id 
        WHERE d.usuario_id = ? AND d.estado = 'Activo'
        ORDER BY d.fecha_subida DESC
        LIMIT 10";

$stmt = $mysqli->prepare($sql);
if (!$stmt) {
    die("❌ Error preparando consulta: " . $mysqli->error);
}

$stmt->bind_param("i", $usuario_id);
$stmt->execute();

// Usar bind_result
$stmt->bind_result($doc_id, $prospecto_id, $doc_usuario_id, $rut_cliente, $nombre_archivo,
                   $nombre_original, $tipo_archivo, $tamaño_archivo, $ruta_archivo, 
                   $fecha_subida, $estado, $razon_social);

$documentos = [];
while ($stmt->fetch()) {
    $documentos[] = [
        'id' => $doc_id,
        'prospecto_id' => $prospecto_id,
        'usuario_id' => $doc_usuario_id,
        'rut_cliente' => $rut_cliente,
        'nombre_archivo' => $nombre_archivo,
        'nombre_original' => $nombre_original,
        'tipo_archivo' => $tipo_archivo,
        'tamaño_archivo' => $tamaño_archivo,
        'ruta_archivo' => $ruta_archivo,
        'fecha_subida' => $fecha_subida,
        'estado' => $estado,
        'razon_social' => $razon_social
    ];
}
$stmt->close();

if (empty($documentos)) {
    echo "<p>⚠️ No se encontraron documentos para este usuario.</p>";
    echo "<p>Para crear documentos de prueba, ve a <a href='form_inteletgroup.php'>form_inteletgroup.php</a></p>";
} else {
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>RUT Cliente</th><th>Razón Social</th><th>Archivo Original</th>";
    echo "<th>Archivo Servidor</th><th>Tipo</th><th>Tamaño</th><th>Ruta</th><th>Estado Archivo</th><th>Acciones</th>";
    echo "</tr>";
    
    foreach ($documentos as $doc) {
        echo "<tr>";
        echo "<td>{$doc['id']}</td>";
        echo "<td>{$doc['rut_cliente']}</td>";
        echo "<td>" . htmlspecialchars($doc['razon_social'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($doc['nombre_original']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['nombre_archivo']) . "</td>";
        echo "<td>" . htmlspecialchars($doc['tipo_archivo']) . "</td>";
        echo "<td>" . number_format($doc['tamaño_archivo'] / 1024, 2) . " KB</td>";
        echo "<td>" . htmlspecialchars($doc['ruta_archivo']) . "</td>";
        
        // Verificar si el archivo existe
        $file_path = $doc['ruta_archivo'];
        $file_exists = false;
        $final_path = '';
        
        if (file_exists($file_path)) {
            $file_exists = true;
            $final_path = $file_path;
        } else {
            // Intentar con ruta relativa
            $file_path_rel = __DIR__ . '/' . $doc['ruta_archivo'];
            if (file_exists($file_path_rel)) {
                $file_exists = true;
                $final_path = $file_path_rel;
            } else {
                // Intentar con ruta construida
                $file_path_constructed = __DIR__ . '/uploads/inteletgroup_prospectos/' . basename($doc['ruta_archivo']);
                if (file_exists($file_path_constructed)) {
                    $file_exists = true;
                    $final_path = $file_path_constructed;
                }
            }
        }
        
        if ($file_exists) {
            echo "<td style='color: green;'>✅ Existe<br><small>$final_path</small></td>";
            echo "<td>";
            // Enlaces usando el script problemático (para comparación)
            echo "<div style='margin-bottom: 5px;'>";
            echo "<strong>Script Original:</strong><br>";
            echo "<a href='descargar_documento.php?id={$doc['id']}&action=view' target='_blank' style='margin-right: 10px; color: red;'>❌ Ver (Falla)</a>";
            echo "<a href='descargar_documento.php?id={$doc['id']}&action=download' style='color: red;'>❌ Descargar (Falla)</a>";
            echo "</div>";

            // Enlaces directos (solución temporal)
            echo "<div>";
            echo "<strong>Acceso Directo (Funciona):</strong><br>";
            echo "<a href='{$doc['ruta_archivo']}' target='_blank' style='margin-right: 10px; color: green;'>✅ Ver Directo</a>";
            echo "<a href='{$doc['ruta_archivo']}' download='{$doc['nombre_original']}' style='color: green;'>✅ Descargar Directo</a>";
            echo "</div>";
            echo "</td>";
        } else {
            echo "<td style='color: red;'>❌ No existe</td>";
            echo "<td>-</td>";
        }
        
        echo "</tr>";
    }
    echo "</table>";
}

echo "<br><hr>";
echo "<h2>🔧 Información de Debug</h2>";
echo "<p><strong>Directorio actual:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Usuario ID:</strong> " . $_SESSION['usuario_id'] . "</p>";
echo "<p><strong>Proyecto:</strong> " . $_SESSION['proyecto'] . "</p>";

// Verificar directorio de uploads
$upload_dir = __DIR__ . '/uploads/inteletgroup_prospectos/';
echo "<p><strong>Directorio de uploads:</strong> $upload_dir</p>";
echo "<p><strong>Directorio existe:</strong> " . (is_dir($upload_dir) ? '✅ Sí' : '❌ No') . "</p>";

if (is_dir($upload_dir)) {
    $files = glob($upload_dir . '*');
    echo "<p><strong>Archivos en directorio:</strong> " . count($files) . "</p>";
    if (!empty($files)) {
        echo "<ul>";
        foreach ($files as $file) {
            echo "<li>" . basename($file) . " (" . filesize($file) . " bytes)</li>";
        }
        echo "</ul>";
    }
}

echo "<br><hr>";
echo "<p><a href='inteletgroup_documentos.php'>🔙 Volver a Gestión de Documentos</a></p>";
echo "<p><a href='form_inteletgroup.php'>➕ Registrar Nuevo Prospecto</a></p>";
?>
