<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Formulario InteletGroup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; margin-bottom: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>Test Formulario InteletGroup - Carga de Archivos</h1>
    
    <form id="testForm" enctype="multipart/form-data">
        <div class="form-group">
            <label>Nombre Ejecutivo:</label>
            <input type="text" name="nombre_ejecutivo" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label>RUT Cliente:</label>
            <input type="text" name="rut_cliente" value="12345678-9" required>
        </div>
        
        <div class="form-group">
            <label>Razón Social:</label>
            <input type="text" name="razon_social" value="EMPRESA TEST LTDA" required>
        </div>
        
        <div class="form-group">
            <label>Rubro:</label>
            <input type="text" name="rubro" value="COMERCIO" required>
        </div>
        
        <div class="form-group">
            <label>Dirección Comercial:</label>
            <textarea name="direccion_comercial" required>Av. Test 123, Santiago</textarea>
        </div>
        
        <div class="form-group">
            <label>Teléfono Celular:</label>
            <input type="tel" name="telefono_celular" value="987654321" required>
        </div>
        
        <div class="form-group">
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label>Número POS:</label>
            <input type="text" name="numero_pos" value="POS123">
        </div>
        
        <div class="form-group">
            <label>Tipo de Cuenta:</label>
            <select name="tipo_cuenta" required>
                <option value="Cuenta Vista">Cuenta Vista</option>
                <option value="Cuenta Corriente">Cuenta Corriente</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Número Cuenta Bancaria:</label>
            <input type="text" name="numero_cuenta_bancaria" value="12345678" required>
        </div>
        
        <div class="form-group">
            <label>Días de Atención:</label>
            <input type="text" name="dias_atencion" value="Lunes a Viernes" required>
        </div>
        
        <div class="form-group">
            <label>Horario de Atención:</label>
            <input type="text" name="horario_atencion" value="09:00 - 18:00" required>
        </div>
        
        <div class="form-group">
            <label>Contrata Boleta:</label>
            <select name="contrata_boleta" required>
                <option value="Si">Sí</option>
                <option value="No">No</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Competencia Actual:</label>
            <select name="competencia_actual" required>
                <option value="Transbank">Transbank</option>
                <option value="Getnet">Getnet</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Documentos (Selecciona uno o más archivos):</label>
            <input type="file" name="documentos[]" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls">
        </div>
        
        <button type="button" onclick="submitForm()">Enviar Formulario</button>
    </form>
    
    <div id="result" class="result" style="display: none;">
        <h3>Resultado:</h3>
        <pre id="resultContent"></pre>
    </div>
    
    <script>
        async function submitForm() {
            const form = document.getElementById('testForm');
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            try {
                const formData = new FormData(form);
                formData.append('usuario_id', 1);
                
                // Debug: Mostrar contenido de FormData
                console.log('Contenido de FormData:');
                for (let pair of formData.entries()) {
                    if (pair[1] instanceof File) {
                        console.log(pair[0], '(File):', pair[1].name, pair[1].size, 'bytes');
                    } else {
                        console.log(pair[0], ':', pair[1]);
                    }
                }
                
                const response = await fetch('debug_files.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.text();
                resultContent.textContent = result;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                console.error('Error:', error);
                resultContent.textContent = 'Error: ' + error.message;
                resultDiv.style.display = 'block';
            }
        }
    </script>
</body>
</html>
