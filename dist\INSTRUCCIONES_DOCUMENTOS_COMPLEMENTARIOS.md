# 📋 INSTRUCCIONES PARA AGREGAR DOCUMENTOS COMPLEMENTARIOS

## 🎯 Objetivo
Agregar 4 nuevos documentos complementarios que aplican tanto para personas **Naturales** como **Jurídicas**:

1. **Mandato PAC**
2. **Cuentas de abono de terceros** 
3. **Poder Notarial para cuentas de terceros**
4. **Sociedad de Hecho - Declaración Jurada Notarial**

## 📝 Pasos a seguir






### 3. 🔧 El código PHP ya está preparado
El archivo `inteletgroup_documentos_enhanced.php` **YA ESTÁ PREPARADO** para manejar estos documentos porque:

- ✅ La consulta en línea 365 incluye: `WHERE (tipo_persona = ? OR tipo_persona = 'Ambos')`
- ✅ El checklist dinámico funcionará automáticamente
- ✅ Los documentos aparecerán en ambos tipos de persona (Natural y Jurídica)

### 4. 🧪 Testing
Después de ejecutar los scripts SQL:

1. **Accede al sistema** con un usuario que tenga prospectos
2. **Ve a "Gestión de Documentos"**
3. **Selecciona un prospecto** (Natural o Jurídica)
4. **Ve al tab "Checklist"**
5. **Verifica que aparezcan los 4 nuevos documentos** en la sección "Documentos Opcionales"

## 📊 Resultado esperado

### Para Persona Natural:
- Documentos Requeridos: 7 (sin cambios)
- **Documentos Opcionales: 7** (3 anteriores + 4 nuevos)

### Para Persona Jurídica:
- Documentos Requeridos: 8 (sin cambios)  
- **Documentos Opcionales: 4** (0 anteriores + 4 nuevos)

## 🔍 Estructura final esperada

```
Documentos Opcionales:
├── Mandato PAC (Ambos)
├── Cuentas de abono de terceros (Ambos) ← NUEVO
├── Poder Notarial para cuentas de terceros (Ambos)
└── Sociedad de Hecho - Declaración Jurada Notarial (Ambos) ← NUEVO
```

## ⚠️ Notas importantes

1. **Tipo 'Ambos'**: Los documentos con `tipo_persona = 'Ambos'` aparecerán tanto para personas Naturales como Jurídicas
2. **Orden**: Los nuevos documentos tienen orden 20-23 para aparecer al final
3. **Opcionales**: Todos los nuevos documentos son opcionales (`es_obligatorio = 0`)
4. **Códigos únicos**: Cada documento tiene un código único que empieza con `AMBOS_`

## 🚀 ¡Listo!
Una vez ejecutados los scripts SQL, los nuevos documentos aparecerán automáticamente en el checklist sin necesidad de cambios adicionales en el código.
