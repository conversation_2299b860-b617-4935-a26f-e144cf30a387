<?php
// Debug simple para verificar recepción de archivos
session_start();

// Simular datos de sesión
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['nombre_usuario'] = 'Test User';
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = [
        'success' => true,
        'message' => 'Datos recibidos correctamente',
        'debug' => [
            'post_data' => $_POST,
            'files_data' => $_FILES,
            'files_count' => isset($_FILES['documentos']) ? count($_FILES['documentos']['name']) : 0
        ]
    ];
    
    // Log para debugging
    error_log("=== DEBUG FILES ===");
    error_log("POST: " . print_r($_POST, true));
    error_log("FILES: " . print_r($_FILES, true));
    
    echo json_encode($response, JSON_PRETTY_PRINT);
} else {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
}
?>
