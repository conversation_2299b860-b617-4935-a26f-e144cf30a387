/* Estilos para el checklist de documentos InteletGroup */

/* Contenedor principal del checklist */
.document-checklist {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

/* Cada item del checklist */
.checklist-item {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.checklist-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Header del item */
.checklist-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.checklist-item-title {
    font-weight: 600;
    color: #212529;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checklist-item-title .required-badge {
    background-color: #dc3545;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 3px;
}

.checklist-item-title .optional-badge {
    background-color: #6c757d;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 3px;
}

/* Descripción del documento */
.checklist-item-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

/* Estado del documento */
.checklist-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.checklist-status.status-pending {
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
}

.checklist-status.status-uploaded {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.checklist-status.status-error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

/* Área de upload */
.checklist-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    background-color: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checklist-upload-area:hover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.checklist-upload-area.drag-over {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    transform: scale(1.02);
}

/* Input file oculto */
.checklist-file-input {
    display: none;
}

/* Lista de archivos subidos */
.uploaded-files-list {
    margin-top: 0.5rem;
}

.uploaded-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.uploaded-file-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #495057;
}

.uploaded-file-actions {
    display: flex;
    gap: 0.25rem;
}

.btn-remove-file {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: all 0.2s;
}

.btn-remove-file:hover {
    background-color: #dc3545;
    color: white;
}

/* Grupo de documentos por categoría */
.document-category {
    margin-bottom: 2rem;
}

.document-category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #0d6efd;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* Responsive */
@media (max-width: 768px) {
    .checklist-item {
        padding: 0.75rem;
    }
    
    .checklist-item-header {
        flex-direction: column;
        align-items: start;
        gap: 0.5rem;
    }
    
    .checklist-upload-area {
        padding: 0.75rem;
    }
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.checklist-item {
    animation: fadeIn 0.3s ease-out;
}

/* Indicador de progreso */
.checklist-progress {
    margin-bottom: 1.5rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.progress-bar-container {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background-color: #28a745;
    transition: width 0.3s ease;
}

/* Estados de validación */
.checklist-item.is-valid {
    border-color: #28a745;
}

.checklist-item.is-invalid {
    border-color: #dc3545;
}

/* Mensajes de ayuda */
.checklist-help-text {
    background-color: #e7f3ff;
    border-left: 4px solid #0d6efd;
    padding: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #004085;
}