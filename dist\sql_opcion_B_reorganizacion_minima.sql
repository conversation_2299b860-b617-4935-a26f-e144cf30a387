-- =====================================================
-- OPCIÓN B: REORGANIZACIÓN MÍNIMA DE DOCUMENTOS
-- =====================================================
-- Este script hace los cambios mínimos necesarios para eliminar duplicados
-- y corregir la obligatoriedad, manteniendo la estructura actual

-- PASO 1: ELIMINAR SOLO LOS DUPLICADOS PROBLEMÁTICOS
-- =====================================================
DELETE FROM tb_inteletgroup_tipos_documento WHERE id = 19; -- AMBOS_MANDATO_PAC (duplicado con ID 8)
DELETE FROM tb_inteletgroup_tipos_documento WHERE id = 21; -- AMBOS_PODER_NOTARIAL (duplicado con ID 9)

-- PASO 2: CORREGIR OBLIGATORIEDAD
-- =====================================================
-- ID 7: Carpeta Tributaria debe ser OBLIGATORIA para Persona Natural
UPDATE tb_inteletgroup_tipos_documento 
SET es_obligatorio = 1 
WHERE id = 7;

-- IDs 17, 18: Carpeta Tributaria y E-Rut obligatorios para Persona Jurídica
UPDATE tb_inteletgroup_tipos_documento 
SET es_obligatorio = 1 
WHERE id IN (17, 18);

-- PASO 3: RENOMBRAR ID 20 PARA MAYOR CLARIDAD
-- =====================================================
UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CUENTAS_TERCEROS',
    nombre = 'Cuentas de abono de terceros',
    descripcion = 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
    orden = 21
WHERE id = 20;

-- PASO 4: AJUSTAR ORDEN DEL ID 22
-- =====================================================
UPDATE tb_inteletgroup_tipos_documento 
SET orden = 22
WHERE id = 22;

-- =====================================================
-- VERIFICACIÓN FINAL
-- =====================================================
-- Ejecutar estas consultas para verificar el resultado:

SELECT 'PERSONA NATURAL - OBLIGATORIOS' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE tipo_persona = 'Natural' AND es_obligatorio = 1 
ORDER BY orden;

SELECT 'PERSONA NATURAL - OPCIONALES' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE tipo_persona = 'Natural' AND es_obligatorio = 0 
ORDER BY orden;

SELECT 'PERSONA JURÍDICA - OBLIGATORIOS' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE tipo_persona = 'Juridica' AND es_obligatorio = 1 
ORDER BY orden;

SELECT 'PERSONA JURÍDICA - OPCIONALES' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE tipo_persona = 'Juridica' AND es_obligatorio = 0 
ORDER BY orden;

SELECT 'DOCUMENTOS TIPO AMBOS' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE tipo_persona = 'Ambos' 
ORDER BY orden;

-- =====================================================
-- RESULTADO ESPERADO:
-- =====================================================
-- PERSONA NATURAL: 7 obligatorios + 3 opcionales = 10 total
-- PERSONA JURÍDICA: 8 obligatorios + 0 opcionales = 8 total
-- DOCUMENTOS TIPO 'AMBOS': 2 opcionales (IDs 20, 22)
-- TOTAL COMBINADO:
--   - Natural: 10 propios + 2 ambos = 12 total
--   - Jurídica: 8 propios + 2 ambos = 10 total
-- =====================================================
