# 🔍 DIAGNÓSTICO - PODER NOTARIAL SIMPLE

## 📊 **ESTADO ACTUAL:**

### ✅ **En Base de Datos:**
```sql
ID: 20
Código: AMBOS_PODER_NOTARIAL_SIMPLE
Nombre: Poder Notarial Simple
Tipo: Ambos
Obligatorio: No (0)
Orden: 10
Estado: Activo
```

### ❌ **En Formulario:**
**NO aparece** el "Poder Notarial Simple"

### 🤔 **Documentos que SÍ aparecen en formulario:**
1. ✅ Carpeta Tributaria (FAPRO) - Orden 7
2. ✅ Mandato PAC - Orden 8  
3. ✅ Poder Notarial - Orden 9
4. ❌ **Poder Notarial Simple - Orden 10** ← FALTA
5. ✅ Carta de Aceptación - Orden 11
6. ✅ Cuentas de abono de terceros ← **NO está en BD**
7. ✅ Sociedad de Hecho - Orden 23

## 🎯 **PROBLEMA IDENTIFICADO:**

### **Teoría 1: Endpoint no funciona correctamente**
- El formulario sigue usando array hardcodeado
- Por eso aparece "Cuentas de abono de terceros" (que no está en BD)
- Por eso NO aparece "Poder Notarial Simple" (que sí está en BD)

### **Teoría 2: Conflicto de cache**
- El endpoint funciona pero hay cache del navegador
- Los cambios no se reflejan inmediatamente

### **Teoría 3: Error en el endpoint**
- El endpoint no devuelve todos los documentos
- Hay un filtro o límite que excluye algunos registros

## 🔧 **PLAN DE ACCIÓN:**

### **PASO 1: Verificar si endpoint funciona**
Probar directamente el endpoint con POST para ver qué devuelve:
```bash
curl -X POST https://www.gestarservicios.cl/intranet/dist/endpoints/obtener_tipos_documento.php \
-H "Content-Type: application/json" \
-d '{"tipo_persona":"Natural"}'
```

### **PASO 2: Agregar logs al JavaScript**
Modificar el JavaScript para mostrar en consola qué documentos recibe:
```javascript
console.log('Documentos recibidos desde BD:', data.documentos);
```

### **PASO 3: Verificar fallback**
Confirmar si está usando el array hardcodeado o la BD:
- Si usa BD: Carpeta Tributaria = opcional, NO aparece "Cuentas de terceros"
- Si usa array: Carpeta Tributaria = obligatoria, SÍ aparece "Cuentas de terceros"

### **PASO 4: Solución temporal**
Si el endpoint no funciona, agregar "Poder Notarial Simple" al array hardcodeado.

## 🎯 **CONCLUSIÓN ACTUAL:**

**El formulario está usando una MEZCLA**:
- ✅ Carpeta Tributaria como opcional (viene de BD)
- ❌ "Cuentas de terceros" aparece (viene de array hardcodeado)
- ❌ "Poder Notarial Simple" no aparece (no está en array hardcodeado)

**Esto sugiere que hay un problema en el endpoint o en el manejo de la respuesta.**

## 📝 **PRÓXIMOS PASOS:**

1. **Probar endpoint directamente**
2. **Revisar logs del navegador**
3. **Agregar "Cuentas de terceros" a la BD** si es necesario
4. **Verificar que "Poder Notarial Simple" aparezca**

¿Quieres que procedamos con alguno de estos pasos?
