-- =====================================================
-- OPCIÓN A: REORGANIZACIÓN COMPLETA DE DOCUMENTOS
-- =====================================================
-- Este script reorganiza completamente la tabla para eliminar duplicados
-- y crear una estructura más limpia y mantenible

-- PASO 1: ELIMINAR DUPLICADOS
-- =====================================================
DELETE FROM tb_inteletgroup_tipos_documento WHERE id = 19; -- AMBOS_MANDATO_PAC (duplicado)
DELETE FROM tb_inteletgroup_tipos_documento WHERE id = 21; -- AMBOS_PODER_NOTARIAL (duplicado)

-- PASO 2: CONVERTIR DOCUMENTOS A TIPO 'AMBOS'
-- =====================================================
-- Cambiar IDs 7, 8, 9, 10 de tipo 'Natural' a tipo 'Ambos'
UPDATE tb_inteletgroup_tipos_documento 
SET tipo_persona = 'Ambos' 
WHERE id IN (7, 8, 9, 10);

-- PASO 3: ACTUALIZAR CÓDIGOS PARA REFLEJAR TIPO 'AMBOS'
-- =====================================================
UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CARPETA_TRIBUTARIA' 
WHERE id = 7;

UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_MANDATO_PAC' 
WHERE id = 8;

UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_PODER_NOTARIAL' 
WHERE id = 9;

UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CARTA_ACEPTACION' 
WHERE id = 10;

-- PASO 4: CORREGIR OBLIGATORIEDAD
-- =====================================================
-- ID 7: Carpeta Tributaria debe ser OPCIONAL (no obligatoria)
UPDATE tb_inteletgroup_tipos_documento
SET es_obligatorio = 0
WHERE id = 7;

-- ID 17: Carpeta Tributaria para Jurídica también debe ser OPCIONAL
UPDATE tb_inteletgroup_tipos_documento
SET es_obligatorio = 0
WHERE id = 17;

-- ID 18: E-Rut debe ser OBLIGATORIO para Jurídica
UPDATE tb_inteletgroup_tipos_documento
SET es_obligatorio = 1
WHERE id = 18;

-- PASO 5: ACTUALIZAR DESCRIPCIONES
-- =====================================================
UPDATE tb_inteletgroup_tipos_documento 
SET descripcion = 'Carpeta tributaria del SII'
WHERE id = 7;

UPDATE tb_inteletgroup_tipos_documento 
SET descripcion = 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento'
WHERE id = 8;

UPDATE tb_inteletgroup_tipos_documento 
SET descripcion = 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento'
WHERE id = 9;

UPDATE tb_inteletgroup_tipos_documento 
SET descripcion = 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada'
WHERE id = 10;

-- PASO 6: RENOMBRAR ID 20 PARA EVITAR CONFUSIÓN
-- =====================================================
UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CUENTAS_TERCEROS',
    nombre = 'Cuentas de abono de terceros',
    descripcion = 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
    orden = 21
WHERE id = 20;

-- PASO 7: AJUSTAR ORDEN DEL ID 22
-- =====================================================
UPDATE tb_inteletgroup_tipos_documento 
SET orden = 22
WHERE id = 22;

-- =====================================================
-- VERIFICACIÓN FINAL
-- =====================================================
-- Ejecutar estas consultas para verificar el resultado:

SELECT 'PERSONA NATURAL - OBLIGATORIOS' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE (tipo_persona = 'Natural' OR tipo_persona = 'Ambos') AND es_obligatorio = 1 
ORDER BY orden;

SELECT 'PERSONA NATURAL - OPCIONALES' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE (tipo_persona = 'Natural' OR tipo_persona = 'Ambos') AND es_obligatorio = 0 
ORDER BY orden;

SELECT 'PERSONA JURÍDICA - OBLIGATORIOS' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE (tipo_persona = 'Juridica' OR tipo_persona = 'Ambos') AND es_obligatorio = 1 
ORDER BY orden;

SELECT 'PERSONA JURÍDICA - OPCIONALES' as categoria;
SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
FROM tb_inteletgroup_tipos_documento 
WHERE (tipo_persona = 'Juridica' OR tipo_persona = 'Ambos') AND es_obligatorio = 0 
ORDER BY orden;

-- =====================================================
-- RESULTADO ESPERADO:
-- =====================================================
-- PERSONA NATURAL: 7 obligatorios + 5 opcionales = 12 total
-- PERSONA JURÍDICA: 8 obligatorios + 5 opcionales = 13 total
-- DOCUMENTOS TIPO 'AMBOS': 6 total (1 obligatorio + 5 opcionales)
-- =====================================================
