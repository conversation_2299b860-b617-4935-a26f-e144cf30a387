<?php
// Configuración de errores
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Headers
header('Content-Type: text/html; charset=utf-8');

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    header('Location: login.php?error=' . urlencode('No autorizado'));
    exit;
}

require_once 'con_db.php';

$mensaje = '';
$tipo = 'danger';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['documento'])) {
    $prospecto_id = $_POST['prospecto_id'] ?? null;
    $rut_cliente = $_POST['rut_cliente'] ?? null;
    $tipo_documento_id = $_POST['tipo_documento_id'] ?? null;
    $usuario_id = $_SESSION['usuario_id'];
    
    if (!$prospecto_id || !$rut_cliente) {
        $mensaje = 'Datos del prospecto no válidos';
    } else {
        $file = $_FILES['documento'];
        
        // Validaciones
        $allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        $max_size = 5 * 1024 * 1024; // 5MB
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $mensaje = 'Error al subir el archivo';
        } elseif (!in_array($file['type'], $allowed_types)) {
            $mensaje = 'Tipo de archivo no permitido';
        } elseif ($file['size'] > $max_size) {
            $mensaje = 'El archivo excede el tamaño máximo permitido (5MB)';
        } else {
            // Procesar archivo
            $upload_dir = 'uploads/inteletgroup_prospectos/';
            
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $unique_name = $rut_cliente . '_' . uniqid() . '.' . $extension;
            $file_path = $upload_dir . $unique_name;
            
            if (move_uploaded_file($file['tmp_name'], $file_path)) {
                // Insertar en base de datos
                $stmt = $mysqli->prepare("
                    INSERT INTO tb_inteletgroup_documentos (
                        prospecto_id, tipo_documento_id, usuario_id, rut_cliente, 
                        nombre_archivo, nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt) {
                    $stmt->bind_param("iiissssss",
                        $prospecto_id, $tipo_documento_id, $usuario_id, $rut_cliente,
                        $unique_name, $file['name'], $file['type'], $file['size'], $file_path
                    );
                    
                    if ($stmt->execute()) {
                        $documento_id = $mysqli->insert_id;
                        
                        // Actualizar checklist si se especificó tipo de documento
                        if ($tipo_documento_id) {
                            $stmt2 = $mysqli->prepare("
                                UPDATE tb_inteletgroup_documento_checklist 
                                SET documento_id = ?, estado = 'Subido' 
                                WHERE prospecto_id = ? AND tipo_documento_id = ?
                            ");
                            $stmt2->bind_param("iii", $documento_id, $prospecto_id, $tipo_documento_id);
                            $stmt2->execute();
                            $stmt2->close();
                        }
                        
                        // Registrar en bitácora
                        $stmt3 = $mysqli->prepare("
                            INSERT INTO tb_inteletgroup_prospecto_bitacora (
                                prospecto_id, usuario_id, accion, descripcion
                            ) VALUES (?, ?, 'Subir Documento', ?)
                        ");
                        $descripcion = "Documento subido: " . $file['name'];
                        $stmt3->bind_param("iis", $prospecto_id, $usuario_id, $descripcion);
                        $stmt3->execute();
                        $stmt3->close();
                        
                        $mensaje = 'Documento subido exitosamente';
                        $tipo = 'success';
                    } else {
                        $mensaje = 'Error al guardar en la base de datos';
                        unlink($file_path); // Eliminar archivo si falla la BD
                    }
                    $stmt->close();
                } else {
                    $mensaje = 'Error al preparar la consulta';
                    unlink($file_path);
                }
            } else {
                $mensaje = 'Error al mover el archivo al servidor';
            }
        }
    }
}

// Redireccionar con mensaje
$_SESSION['upload_message'] = $mensaje;
$_SESSION['upload_type'] = $tipo;
header('Location: inteletgroup_documentos_enhanced.php?prospecto_id=' . ($prospecto_id ?? ''));
exit;
?>