-- Opción 1: Deshabilitar verificaciones de FK temporalmente
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE tb_inteletgroup_documentos;
SET FOREIGN_KEY_CHECKS = 1;

-- Opción 2: Eliminar registros en orden correcto
-- Primero eliminar de la tabla que referencia
DELETE FROM tb_inteletgroup_documento_checklist WHERE documento_id IN (
    SELECT id FROM tb_inteletgroup_documentos
);
-- Luego truncar la tabla principal
TRUNCATE TABLE tb_inteletgroup_documentos;

-- Opción 3: Usar DELETE en lugar de TRUNCATE
DELETE FROM tb_inteletgroup_documentos;

-- Opción 4: Resetear el AUTO_INCREMENT después del DELETE
DELETE FROM tb_inteletgroup_documentos;
ALTER TABLE tb_inteletgroup_documentos AUTO_INCREMENT = 1;
