<?php
// =================================================================
// Script de Debug: Investigar problemas de documentos en producción
// =================================================================

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar autenticación (simulada para pruebas)
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1; // Cambiar por un ID válido
    $_SESSION['proyecto'] = 'inteletGroup';
    $_SESSION['nombre_usuario'] = 'Usuario de Debug';
}

require_once 'con_db.php';

echo "<h1>🔍 Debug de Problemas de Documentos</h1>";
echo "<hr>";

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    die("❌ Error de conexión a la base de datos");
}

$usuario_id = $_SESSION['usuario_id'];
echo "<h2>📋 Información del Usuario</h2>";
echo "<p><strong>Usuario ID:</strong> $usuario_id</p>";
echo "<p><strong>Proyecto:</strong> " . $_SESSION['proyecto'] . "</p>";

// Obtener documentos específicos que están fallando
echo "<h2>📄 Documentos Problemáticos</h2>";

$document_ids = [3, 4]; // IDs de los documentos que están fallando

foreach ($document_ids as $doc_id) {
    echo "<h3>Documento ID: $doc_id</h3>";
    
    // Consultar información del documento
    $sql = "SELECT d.id, d.prospecto_id, d.usuario_id, d.rut_cliente, d.nombre_archivo, 
                   d.nombre_original, d.tipo_archivo, d.tamaño_archivo, d.ruta_archivo, 
                   d.fecha_subida, d.estado, p.usuario_id as propietario_id 
            FROM tb_inteletgroup_documentos d 
            INNER JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id 
            WHERE d.id = ?";
    
    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        echo "<p>❌ Error preparando consulta: " . $mysqli->error . "</p>";
        continue;
    }
    
    $stmt->bind_param("i", $doc_id);
    $stmt->execute();
    
    $stmt->bind_result($d_id, $prospecto_id, $d_usuario_id, $rut_cliente, $nombre_archivo,
                       $nombre_original, $tipo_archivo, $tamaño_archivo, $ruta_archivo, 
                       $fecha_subida, $estado, $propietario_id);
    
    if ($stmt->fetch()) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><td><strong>ID</strong></td><td>$d_id</td></tr>";
        echo "<tr><td><strong>Prospecto ID</strong></td><td>$prospecto_id</td></tr>";
        echo "<tr><td><strong>Usuario ID</strong></td><td>$d_usuario_id</td></tr>";
        echo "<tr><td><strong>Propietario ID</strong></td><td>$propietario_id</td></tr>";
        echo "<tr><td><strong>RUT Cliente</strong></td><td>$rut_cliente</td></tr>";
        echo "<tr><td><strong>Nombre Archivo</strong></td><td>$nombre_archivo</td></tr>";
        echo "<tr><td><strong>Nombre Original</strong></td><td>$nombre_original</td></tr>";
        echo "<tr><td><strong>Tipo Archivo</strong></td><td>$tipo_archivo</td></tr>";
        echo "<tr><td><strong>Tamaño</strong></td><td>$tamaño_archivo bytes</td></tr>";
        echo "<tr><td><strong>Ruta Archivo</strong></td><td>$ruta_archivo</td></tr>";
        echo "<tr><td><strong>Estado</strong></td><td>$estado</td></tr>";
        echo "<tr><td><strong>Fecha Subida</strong></td><td>$fecha_subida</td></tr>";
        echo "</table>";
        
        // Verificar si el archivo existe
        echo "<h4>🔍 Verificación de Archivo</h4>";
        $file_path = $ruta_archivo;
        $original_path = $file_path;
        
        echo "<p><strong>Ruta original:</strong> $original_path</p>";
        echo "<p><strong>Archivo existe (ruta original):</strong> " . (file_exists($file_path) ? '✅ SÍ' : '❌ NO') . "</p>";
        
        if (!file_exists($file_path)) {
            // Intentar con ruta relativa
            $file_path = __DIR__ . '/' . $ruta_archivo;
            echo "<p><strong>Ruta relativa:</strong> $file_path</p>";
            echo "<p><strong>Archivo existe (ruta relativa):</strong> " . (file_exists($file_path) ? '✅ SÍ' : '❌ NO') . "</p>";
            
            if (!file_exists($file_path)) {
                // Intentar con ruta construida
                $file_path = __DIR__ . '/uploads/inteletgroup_prospectos/' . basename($ruta_archivo);
                echo "<p><strong>Ruta construida:</strong> $file_path</p>";
                echo "<p><strong>Archivo existe (ruta construida):</strong> " . (file_exists($file_path) ? '✅ SÍ' : '❌ NO') . "</p>";
            }
        }
        
        if (file_exists($file_path)) {
            $real_size = filesize($file_path);
            echo "<p><strong>Tamaño real del archivo:</strong> $real_size bytes</p>";
            echo "<p><strong>Coincide con BD:</strong> " . ($real_size == $tamaño_archivo ? '✅ SÍ' : '❌ NO') . "</p>";
            
            // Verificar tipo MIME real
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $real_mime = finfo_file($finfo, $file_path);
            finfo_close($finfo);
            echo "<p><strong>Tipo MIME real:</strong> $real_mime</p>";
            echo "<p><strong>Coincide con BD:</strong> " . ($real_mime == $tipo_archivo ? '✅ SÍ' : '❌ NO') . "</p>";
        }
        
        // Verificar permisos de usuario
        echo "<h4>🔐 Verificación de Permisos</h4>";
        echo "<p><strong>Usuario actual:</strong> $usuario_id</p>";
        echo "<p><strong>Propietario del documento:</strong> $propietario_id</p>";
        echo "<p><strong>Tiene permisos:</strong> " . ($usuario_id == $propietario_id ? '✅ SÍ' : '❌ NO') . "</p>";
        
        // Enlaces de prueba
        echo "<h4>🧪 Enlaces de Prueba</h4>";
        echo "<p><a href='descargar_documento.php?id=$doc_id&action=view' target='_blank'>👁️ Ver Documento</a></p>";
        echo "<p><a href='descargar_documento.php?id=$doc_id&action=download'>💾 Descargar Documento</a></p>";
        
    } else {
        echo "<p>❌ Documento no encontrado en la base de datos</p>";
    }
    
    $stmt->close();
    echo "<hr>";
}

// Verificar directorio de uploads
echo "<h2>📁 Verificación de Directorio de Uploads</h2>";
$upload_dir = __DIR__ . '/uploads/inteletgroup_prospectos/';
echo "<p><strong>Directorio:</strong> $upload_dir</p>";
echo "<p><strong>Existe:</strong> " . (is_dir($upload_dir) ? '✅ SÍ' : '❌ NO') . "</p>";

if (is_dir($upload_dir)) {
    echo "<p><strong>Permisos:</strong> " . substr(sprintf('%o', fileperms($upload_dir)), -4) . "</p>";
    $files = glob($upload_dir . '*');
    echo "<p><strong>Archivos encontrados:</strong> " . count($files) . "</p>";
    
    if (!empty($files)) {
        echo "<ul>";
        foreach ($files as $file) {
            $size = filesize($file);
            $perms = substr(sprintf('%o', fileperms($file)), -4);
            echo "<li>" . basename($file) . " ($size bytes, permisos: $perms)</li>";
        }
        echo "</ul>";
    }
}

echo "<br><hr>";
echo "<p><a href='inteletgroup_documentos.php'>🔙 Volver a Gestión de Documentos</a></p>";
?>
