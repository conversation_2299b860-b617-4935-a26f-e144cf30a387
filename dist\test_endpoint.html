<!DOCTYPE html>
<html>
<head>
    <title>Test Endpoint</title>
</head>
<body>
    <h1>Test obtener_tipos_documento.php</h1>
    <button onclick="testEndpoint()">Test Endpoint</button>
    <div id="result"></div>

    <script>
    function testEndpoint() {
        console.log('Testing endpoint...');
        
        fetch('endpoints/obtener_tipos_documento.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ tipo_persona: 'Natural' })
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text(); // Usar text() en lugar de json() para ver el error
        })
        .then(data => {
            console.log('Response data:', data);
            document.getElementById('result').innerHTML = '<pre>' + data + '</pre>';
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('result').innerHTML = '<pre>Error: ' + error + '</pre>';
        });
    }
    </script>
</body>
</html>
