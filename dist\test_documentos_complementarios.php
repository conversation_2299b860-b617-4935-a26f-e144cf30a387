<?php
/**
 * Script de prueba para verificar los documentos complementarios
 * Ejecutar después de agregar los nuevos documentos a la BD
 */

require_once 'con_db.php';

echo "<h2>🧪 Test de Documentos Complementarios</h2>";

// 1. Verificar que los nuevos documentos existen en la BD
echo "<h3>1. ✅ Verificando documentos en la base de datos</h3>";

$query = "SELECT codigo, nombre, tipo_persona, es_obligatorio, orden 
          FROM tb_inteletgroup_tipos_documento 
          WHERE codigo LIKE 'AMBOS_%' 
          ORDER BY orden";

$result = $conexion->query($query);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Código</th><th>Nombre</th><th>Tipo Persona</th><th>Obligatorio</th><th>Orden</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['codigo']) . "</td>";
        echo "<td>" . htmlspecialchars($row['nombre']) . "</td>";
        echo "<td>" . htmlspecialchars($row['tipo_persona']) . "</td>";
        echo "<td>" . ($row['es_obligatorio'] ? 'Sí' : 'No') . "</td>";
        echo "<td>" . $row['orden'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p style='color: green;'>✅ Se encontraron " . $result->num_rows . " documentos complementarios</p>";
} else {
    echo "<p style='color: red;'>❌ No se encontraron documentos complementarios. Ejecuta primero el script SQL.</p>";
}

// 2. Verificar que la consulta del checklist funciona
echo "<h3>2. 🔍 Verificando consulta del checklist</h3>";

$test_tipos = ['Natural', 'Juridica'];

foreach ($test_tipos as $tipo_persona) {
    echo "<h4>Tipo de persona: $tipo_persona</h4>";
    
    $stmt = $conexion->prepare("
        SELECT id, codigo, nombre, descripcion, es_obligatorio, orden
        FROM tb_inteletgroup_tipos_documento
        WHERE (tipo_persona = ? OR tipo_persona = 'Ambos') AND estado = 'Activo'
        ORDER BY orden ASC
    ");
    
    if ($stmt) {
        $stmt->bind_param("s", $tipo_persona);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $obligatorios = 0;
        $opcionales = 0;
        
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            $tipo = $row['es_obligatorio'] ? 'Obligatorio' : 'Opcional';
            $color = $row['es_obligatorio'] ? 'blue' : 'green';
            
            if ($row['es_obligatorio']) {
                $obligatorios++;
            } else {
                $opcionales++;
            }
            
            echo "<li style='color: $color;'>";
            echo "<strong>" . htmlspecialchars($row['nombre']) . "</strong> ";
            echo "($tipo) - Orden: " . $row['orden'];
            echo "</li>";
        }
        echo "</ul>";
        
        echo "<p><strong>Resumen:</strong> $obligatorios obligatorios, $opcionales opcionales</p>";
        $stmt->close();
    }
}

// 3. Verificar contadores
echo "<h3>3. 📊 Resumen por tipo de persona</h3>";

$query = "SELECT 
    tipo_persona,
    COUNT(*) as total_documentos,
    SUM(CASE WHEN es_obligatorio = 1 THEN 1 ELSE 0 END) as obligatorios,
    SUM(CASE WHEN es_obligatorio = 0 THEN 1 ELSE 0 END) as opcionales
FROM tb_inteletgroup_tipos_documento 
WHERE estado = 'Activo'
GROUP BY tipo_persona
ORDER BY tipo_persona";

$result = $conexion->query($query);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Tipo Persona</th><th>Total</th><th>Obligatorios</th><th>Opcionales</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['tipo_persona']) . "</td>";
        echo "<td>" . $row['total_documentos'] . "</td>";
        echo "<td>" . $row['obligatorios'] . "</td>";
        echo "<td>" . $row['opcionales'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>4. 🎯 Resultado esperado</h3>";
echo "<p><strong>Natural:</strong> Debería tener 4 documentos opcionales adicionales (de tipo 'Ambos')</p>";
echo "<p><strong>Jurídica:</strong> Debería tener 4 documentos opcionales adicionales (de tipo 'Ambos')</p>";
echo "<p><strong>Ambos:</strong> Debería mostrar exactamente 4 documentos</p>";

$conexion->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
h2 { color: #333; }
h3 { color: #666; border-bottom: 1px solid #ccc; }
</style>
