<?php
/**
 * Endpoint simplificado para diagnóstico
 */

// Headers para CORS y JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Log inicial
error_log("obtener_tipos_documento_simple.php: INICIO DEL ENDPOINT");

// Solo permitir POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("obtener_tipos_documento_simple.php: Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Método no permitido'
    ]);
    exit;
}

try {
    error_log("obtener_tipos_documento_simple.php: Iniciando procesamiento POST");
    
    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    error_log("obtener_tipos_documento_simple.php: Input recibido: " . json_encode($input));
    
    // Respuesta simple de éxito
    echo json_encode([
        'success' => true,
        'message' => 'Endpoint funcionando correctamente',
        'input_recibido' => $input,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    error_log("obtener_tipos_documento_simple.php: Respuesta enviada exitosamente");
    
} catch (Exception $e) {
    error_log("obtener_tipos_documento_simple.php: Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

error_log("obtener_tipos_documento_simple.php: FIN DEL ENDPOINT");
?>
