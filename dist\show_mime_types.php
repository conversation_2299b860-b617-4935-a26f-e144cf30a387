<?php
// =================================================================
// Script: Mostrar tipos MIME permitidos en descargar_documento.php
// =================================================================

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die("❌ No autorizado");
}

echo "<h1>🔍 Tipos MIME Permitidos Actuales</h1>";
echo "<hr>";

$file_path = __DIR__ . '/descargar_documento.php';

if (!file_exists($file_path)) {
    die("❌ Archivo descargar_documento.php no encontrado");
}

$content = file_get_contents($file_path);
$last_modified = date('Y-m-d H:i:s', filemtime($file_path));

echo "<p><strong>Archivo:</strong> $file_path</p>";
echo "<p><strong>Última modificación:</strong> $last_modified</p>";

// Buscar la sección de tipos permitidos
$start_pos = strpos($content, '$allowed_types = [');
if ($start_pos !== false) {
    $end_pos = strpos($content, '];', $start_pos);
    if ($end_pos !== false) {
        $allowed_section = substr($content, $start_pos, $end_pos - $start_pos + 2);
        echo "<h2>📋 Sección de Tipos Permitidos:</h2>";
        echo "<pre style='background: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo htmlspecialchars($allowed_section);
        echo "</pre>";
        
        // Verificar si text/plain está incluido
        $has_text_plain = strpos($allowed_section, "'text/plain'") !== false;
        echo "<h2>🔍 Verificación:</h2>";
        echo "<p><strong>Incluye 'text/plain':</strong> " . ($has_text_plain ? '✅ SÍ' : '❌ NO') . "</p>";
        
        if (!$has_text_plain) {
            echo "<h2>🔧 Corrección Necesaria:</h2>";
            echo "<p>❌ El archivo TXT no funcionará porque 'text/plain' no está en la lista.</p>";
            echo "<p>✅ Necesitas agregar 'text/plain' a la lista de tipos permitidos.</p>";
        }
        
    } else {
        echo "<p>❌ No se encontró el final de la sección de tipos permitidos</p>";
    }
} else {
    echo "<p>❌ No se encontró la sección de tipos permitidos</p>";
}

// Buscar también la sección de headers
echo "<h2>📋 Headers de Visualización:</h2>";
$view_pos = strpos($content, "if (\$action === 'view')");
if ($view_pos !== false) {
    $view_end = strpos($content, "} else {", $view_pos);
    if ($view_end !== false) {
        $view_section = substr($content, $view_pos, $view_end - $view_pos);
        echo "<pre style='background: #f0f8ff; padding: 15px; border: 1px solid #ccc; border-radius: 5px;'>";
        echo htmlspecialchars($view_section);
        echo "</pre>";
    }
} else {
    echo "<p>❌ No se encontró la sección de headers de visualización</p>";
}

echo "<hr>";
echo "<h2>🧪 Pruebas:</h2>";
echo "<p><a href='descargar_documento.php?id=4&action=view' target='_blank'>👁️ Probar TXT (debería fallar)</a></p>";
echo "<p><a href='descargar_documento.php?id=3&action=view' target='_blank'>👁️ Probar PDF (debería descargar en lugar de mostrar)</a></p>";
?>
