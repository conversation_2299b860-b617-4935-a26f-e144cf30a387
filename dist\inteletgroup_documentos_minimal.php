<?php
// Versión mínima para probar funcionamiento básico
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Incluir dependencias
require_once 'cache_utils.php';
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    header('Location: login.php?error=' . urlencode('No autorizado'));
    exit;
}

// Incluir conexión a la base de datos
require_once 'con_db.php';

if (!isset($mysqli)) {
    die("Error: Variable mysqli no definida.");
}

$conexion = $mysqli;
$usuario_id = $_SESSION['usuario_id'];
$prospecto_id = isset($_GET['prospecto_id']) ? intval($_GET['prospecto_id']) : null;

// HTML básico
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Documentos (Versión Mínima)</title>
    <?php echo no_cache_meta(); ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>InteletGroup - Gestión de Documentos</h1>
        <p>Versión mínima de prueba</p>
        
        <?php if ($prospecto_id): ?>
            <div class="alert alert-info">
                <h4>Prospecto ID: <?php echo $prospecto_id; ?></h4>
                <?php
                // Buscar información del prospecto
                $stmt = $conexion->prepare("SELECT razon_social, rut_cliente FROM tb_inteletgroup_prospectos WHERE id = ?");
                if ($stmt) {
                    $stmt->bind_param("i", $prospecto_id);
                    $stmt->execute();
                    $stmt->bind_result($razon_social, $rut_cliente);
                    if ($stmt->fetch()) {
                        echo "<p>Razón Social: " . htmlspecialchars($razon_social) . "</p>";
                        echo "<p>RUT: " . htmlspecialchars($rut_cliente) . "</p>";
                    } else {
                        echo "<p>Prospecto no encontrado</p>";
                    }
                    $stmt->close();
                }
                ?>
            </div>
            
            <h3>Documentos del Prospecto</h3>
            <?php
            // Listar documentos
            $stmt = $conexion->prepare("
                SELECT id, nombre_original, tipo_archivo, fecha_subida 
                FROM tb_inteletgroup_documentos 
                WHERE prospecto_id = ? AND estado = 'Activo'
                ORDER BY fecha_subida DESC
            ");
            
            if ($stmt) {
                $stmt->bind_param("i", $prospecto_id);
                $stmt->execute();
                $stmt->bind_result($doc_id, $nombre_original, $tipo_archivo, $fecha_subida);
                
                echo '<ul class="list-group">';
                $count = 0;
                while ($stmt->fetch()) {
                    $count++;
                    echo '<li class="list-group-item">';
                    echo htmlspecialchars($nombre_original) . ' - ' . htmlspecialchars($tipo_archivo) . ' - ' . $fecha_subida;
                    echo '</li>';
                }
                
                if ($count == 0) {
                    echo '<li class="list-group-item">No hay documentos cargados</li>';
                }
                echo '</ul>';
                
                $stmt->close();
            }
            ?>
        <?php else: ?>
            <div class="alert alert-warning">
                No se especificó un prospecto. Use el parámetro ?prospecto_id=XX
            </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="inteletgroup_documentos_enhanced.php<?php echo $prospecto_id ? '?prospecto_id=' . $prospecto_id : ''; ?>" class="btn btn-primary">
                Ir a Versión Completa
            </a>
            <a href="form_inteletgroup.php" class="btn btn-secondary">
                Volver al Formulario
            </a>
        </div>
    </div>
</body>
</html>