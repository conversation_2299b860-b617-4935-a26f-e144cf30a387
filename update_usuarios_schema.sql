CREATE TABLE `tb_inteletgroup_prospectos` (
    `id` int NOT NULL AUTO_INCREMENT,
    `usuario_id` int NOT NULL,
    `nombre_ejecutivo` varchar(255) NOT NULL,
    `rut_cliente` varchar(20) NOT NULL,
    `razon_social` varchar(255) NOT NULL,
    `rubro` varchar(255) NOT NULL,
    `direccion_comercial` text NOT NULL,
    `telefono_celular` varchar(15) NOT NULL,
    `email` varchar(255) NOT NULL,
    `numero_pos` varchar(50) DEFAULT NULL,
    `tipo_cuenta` enum(
        'Cuenta Vista',
        'Cuenta Corriente'
    ) NOT NULL,
    `numero_cuenta_bancaria` varchar(50) NOT NULL,
    `dias_atencion` varchar(255) NOT NULL,
    `horario_atencion` varchar(255) NOT NULL,
    `contrata_boleta` enum('Si', 'No') NOT NULL,
    `competencia_actual` enum(
        'Transbank',
        'Getnet',
        'Compra Aquí (Bco Estado)',
        'Klap',
        'SumUp',
        'Tuu',
        'Ya G<PERSON>ste',
        'Mercado Pago'
    ) NOT NULL,
    `documentos_adjuntos` text,
    `fecha_registro` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `fecha_actualizacion` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `estado` enum('Activo', 'Inactivo') DEFAULT 'Activo',
    PRIMARY KEY (`id`),
    KEY `idx_rut_cliente` (`rut_cliente`),
    KEY `idx_usuario_id` (`usuario_id`),
    KEY `idx_fecha_registro` (`fecha_registro`),
    CONSTRAINT `tb_inteletgroup_prospectos_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `tb_experian_usuarios` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 34 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci