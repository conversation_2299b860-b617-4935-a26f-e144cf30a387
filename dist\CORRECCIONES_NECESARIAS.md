# 🔧 CORRECCIONES NECESARIAS para descargar_documento.php

## 📋 Problemas Identificados con Playwright:

1. **Archivo TXT:** ❌ "Tipo de archivo no permitido" 
2. **Archivo PDF:** ❌ Se descarga automáticamente en lugar de mostrarse

## 🎯 CORRECCIÓN 1: Agregar text/plain a tipos permitidos

**Buscar esta sección en descargar_documento.php:**
```php
$allowed_types = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];
```

**REEMPLAZAR por:**
```php
$allowed_types = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'application/rtf',
    'application/zip',
    'application/x-zip-compressed'
];
```

## 🎯 CORRECCIÓN 2: Arreglar headers de visualización

**Buscar esta sección:**
```php
// Configurar headers según la acción
if ($action === 'view') {
    // Para visualización en el navegador
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: inline; filename="' . $file_name . '"');
} else {
    // Para descarga
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
}
```

**REEMPLAZAR por:**
```php
// Configurar headers según la acción
if ($action === 'view') {
    // Para visualización en el navegador
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: inline; filename="' . addslashes($file_name) . '"');
    header('X-Content-Type-Options: nosniff');
} else {
    // Para descarga
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . addslashes($file_name) . '"');
}
```

## 🎯 CORRECCIÓN 3: Mejorar logging (opcional pero recomendado)

**Agregar después de obtener los parámetros:**
```php
$documento_id = (int)$_GET['id'];
$usuario_id = $_SESSION['usuario_id'];
$action = $_GET['action'] ?? 'download';

// Log inicial para debugging
error_log("=== DESCARGA DOCUMENTO ===");
error_log("Documento ID: $documento_id");
error_log("Usuario ID: $usuario_id");
error_log("Acción: $action");
```

## 🧪 VERIFICACIÓN:

Después de aplicar las correcciones, probar:

1. **Archivo TXT:** https://www.gestarservicios.cl/intranet/dist/descargar_documento.php?id=4&action=view
   - ✅ Debería mostrar el contenido del archivo en el navegador

2. **Archivo PDF:** https://www.gestarservicios.cl/intranet/dist/descargar_documento.php?id=3&action=view
   - ✅ Debería mostrar el PDF en el navegador (no descargar)

3. **Descargas:** 
   - https://www.gestarservicios.cl/intranet/dist/descargar_documento.php?id=4&action=download
   - https://www.gestarservicios.cl/intranet/dist/descargar_documento.php?id=3&action=download
   - ✅ Deberían descargar los archivos

## 📝 RESUMEN:

- **Problema principal:** Lista de tipos MIME incompleta
- **Problema secundario:** Headers de visualización necesitan mejoras
- **Solución:** Agregar tipos faltantes y mejorar headers

Una vez aplicadas estas correcciones, tanto la visualización como la descarga deberían funcionar perfectamente.
